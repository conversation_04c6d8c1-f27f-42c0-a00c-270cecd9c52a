// LinkedIn Comment Assistant - Content Script

// Enable debug mode to see detailed logs in the console
const DEBUG = true;

// Helper function for logging
function debugLog(...args) {
    if (DEBUG) {
        console.log("[Comment Assistant]", ...args);
    }
}

class CommentAssistant {
    constructor() {
        this.observer = null;
        this.addedButtons = new Set();
        debugLog("Initializing Comment Assistant");
        this.init();
    }

    init() {
        // Wait for page to load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.startObserving());
        } else {
            this.startObserving();
        }
    }

    startObserving() {
        // Initial scan
        this.addCommentIcons();

        // Set up mutation observer to catch dynamically loaded content
        this.observer = new MutationObserver((mutations) => {
            let shouldScan = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldScan = true;
                }
            });

            if (shouldScan) {
                setTimeout(() => this.addCommentIcons(), 100);
            }
        });

        this.observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    addCommentIcons() {
        // LinkedIn comment boxes
        const linkedinCommentBoxes = document.querySelectorAll('.comments-comment-box__form');

        linkedinCommentBoxes.forEach((commentBox) => {
            this.addCommentIconToLinkedIn(commentBox);
        });
    }

    addCommentIconToLinkedIn(commentBox) {
        // Check if we already added a button to this comment box
        const existingButton = commentBox.querySelector('.comment-assistant-icon');
        if (existingButton) return;

        // Find the container with other buttons (emoji, photo)
        const buttonContainer = commentBox.querySelector('.display-flex.justify-space-between .display-flex:first-child');

        if (buttonContainer) {
            const commentButton = this.createCommentButton();

            // Insert as the first icon in the button container
            buttonContainer.insertAdjacentElement('afterbegin', commentButton);
        }
    }


    createCommentButton() {
        const button = document.createElement('button');
        button.className = `comment-assistant-icon comment-assistant-icon--linkedin`;
        button.type = 'button';
        button.title = 'Generate AI Comment Suggestion';
        button.setAttribute('aria-label', 'Generate AI Comment Suggestion');

        // Create comment icon using the custom Essenca icon
        button.innerHTML = `
      <div class="comment-assistant-icon__content">
        <svg class="comment-assistant-icon__icon" width="20" height="20" viewBox="0 0 900.000000 900.000000" preserveAspectRatio="xMidYMid meet">
          <g transform="translate(0.000000,900.000000) scale(0.100000,-0.100000)" fill="#DA7756" stroke="none">
            <path d="M4428 8433 c-6 -7 -21 -13 -35 -13 -31 0 -66 -23 -125 -82 -32 -31 -48 -56 -48 -72 0 -36 18 -41 32 -9 7 15 23 38 36 52 12 13 22 28 22 32 0 5 6 9 14 9 7 0 19 7 26 15 7 8 23 15 36 15 12 0 26 5 29 10 9 14 117 12 166 -2 46 -14 126 -86 147 -131 14 -32 32 -27 32 9 0 16 -21 44 -66 89 -54 53 -72 65 -98 65 -18 0 -37 5 -44 12 -16 16 -111 16 -124 1z"/>
            <path d="M4190 7061 c0 -1120 2 -1193 28 -1167 3 4 6 528 7 1164 0 1094 -1 1157 -17 1160 -17 3 -18 -57 -18 -1157z"/>
            <path d="M4760 8218 c0 -1 -1 -525 -1 -1163 -1 -1060 0 -1160 15 -1163 15 -3 16 94 14 1159 -2 919 -6 1163 -15 1167 -7 2 -13 2 -13 0z"/>
          </g>
        </svg>
        <svg class="comment-assistant-icon__spinner" width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M12 4V2A10 10 0 0 0 2 12H4A8 8 0 0 1 12 4Z" fill="#DA7756"/>
        </svg>
      </div>
    `;

        // Add click handler
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleCommentIconClick(button);
        });

        return button;
    }

    handleCommentIconClick(button) {
        debugLog(`Icon clicked on platform: linkedin`);

        const postContainer = button.closest('.feed-shared-update-v2');
        const postContentElement = postContainer?.querySelector('.update-components-text.update-components-update-v2__commentary, .update-components-text');
        const postContent = postContentElement?.textContent.trim();

        if (!postContent) {
            debugLog("LinkedIn Post content not found.");
            alert("Could not find the post content to analyze.");
            return;
        }

        debugLog("LinkedIn Post Content:", postContent);

        // Show loading state
        button.classList.add('comment-assistant-icon--loading');

        // Send post content to the background script for AI processing
        chrome.runtime.sendMessage({
            action: 'generate_linkedin_comment',
            content: postContent
        }, (response) => {
            // Remove loading state
            button.classList.remove('comment-assistant-icon--loading');

            if (response.success) {
                // Show success state briefly
                button.classList.add('comment-assistant-icon--success');
                setTimeout(() => {
                    button.classList.remove('comment-assistant-icon--success');
                }, 2000);

                // Insert the AI-generated comment
                this.insertAIComment(button, response.result);
            } else {
                // Handle error
                console.error("AI comment generation failed:", response.error);
                alert(`Error generating comment: ${response.error}`);
            }
        });
    }

    insertAIComment(button, comment) {
        debugLog(`Inserting AI comment for platform: linkedin`);

        const commentBox = button.closest('.comments-comment-box__form');
        const textEditor = commentBox?.querySelector('.ql-editor');

        if (textEditor) {
            // Focus the editor first
            textEditor.focus();

            // The editor might have a <p> tag already. We need to insert the text there.
            let p = textEditor.querySelector('p');
            if (!p) {
                // If no <p> tag, create one
                p = document.createElement('p');
                textEditor.appendChild(p);
            }

            // Clear existing content and set the new comment
            p.textContent = comment;

            // Remove the placeholder class
            textEditor.classList.remove('ql-blank');

            // Dispatch events to notify LinkedIn's framework (React) of the change.
            const events = ['input', 'change', 'blur', 'focus'];
            events.forEach(eventType => {
                textEditor.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));
            });

            debugLog("Inserted AI comment for LinkedIn using robust method.");
        } else {
            debugLog("LinkedIn text editor (.ql-editor) not found.");
        }
    }

    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
    }

    // Helper to inject a script into the page's context
    injectScript(func, args) {
        const script = document.createElement('script');
        script.textContent = `(${func.toString()}).apply(this, ${JSON.stringify(args)});`;
        document.head.appendChild(script);
        script.remove(); // Clean up the script tag immediately after execution
    }
}

// Initialize the extension
let commentAssistant = null;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        commentAssistant = new CommentAssistant();
    });
} else {
    commentAssistant = new CommentAssistant();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (commentAssistant) {
        commentAssistant.destroy();
    }
});
