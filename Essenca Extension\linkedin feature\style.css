/* LinkedIn Comment Assistant Styles */

.comment-assistant-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
    background: transparent;
    color: #DA7756;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    width: 42px;
    height: 42px;
    min-width: 42px;
    min-height: 42px;
}

.comment-assistant-icon:hover {
    background: rgba(189, 93, 58, 0.2);
    transform: translateY(-1px);
    box-shadow: none;
}

.comment-assistant-icon:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(184, 175, 132, 0.3);
}

.comment-assistant-icon__content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.comment-assistant-icon__icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}

/* Remove the text styling since we removed the text */
.comment-assistant-icon__text {
    display: none;
}

/* Loading state */
.comment-assistant-icon--loading {
    background: transparent;
    cursor: wait;
}

.comment-assistant-icon--loading:hover {
    background: transparent;
    transform: none;
    box-shadow: none;
}

.comment-assistant-icon--loading .comment-assistant-icon__icon path {
    stroke: #888;
}

.comment-assistant-icon__spinner {
    display: none;
    animation: spin 1s linear infinite;
}

.comment-assistant-icon--loading .comment-assistant-icon__icon {
    display: none;
}

.comment-assistant-icon--loading .comment-assistant-icon__spinner {
    display: block;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* Success state */
.comment-assistant-icon--success {
    background: transparent;
}

.comment-assistant-icon--success:hover {
    background: transparent;
}

.comment-assistant-icon--success .comment-assistant-icon__icon path {
    stroke: #0a8754;
}


/* Integration with LinkedIn's button styling */
.comments-comment-box__form .comment-assistant-icon {
    margin-right: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .comment-assistant-icon {
        width: 38px;
        height: 38px;
        min-width: 38px;
        min-height: 38px;
        padding: 6px;
    }

    .comment-assistant-icon__icon {
        width: 20px;
        height: 20px;
    }

}

/* Ensure button doesn't interfere with existing layout */
.comment-assistant-icon * {
    pointer-events: none;
}

/* Animation for button appearance */
.comment-assistant-icon {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .comment-assistant-icon {
        background: #000 !important;
        border: 2px solid #fff;
    }

    .comment-assistant-icon:hover {
        background: #333 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .comment-assistant-icon {
        animation: none;
        transition: none;
    }

    .comment-assistant-icon:hover {
        transform: none;
    }

    .comment-assistant-icon__spinner {
        animation: none;
    }
}