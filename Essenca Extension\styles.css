:root {
  --primary-color: #DA7756;
  --primary-hover: #BD5D3A;
  --secondary-color: #DDD9C5;
  --text-color: #3D3929;
  --text-light: #6D6A58;
  --border-color: #CBC4A4;
  --ai-message-bg: #F0EEE5;
  --user-message-bg: #DDD9C5;
  --shadow-light: 0 2px 10px rgba(184, 175, 132, 0.1);
  --shadow-medium: 0 4px 20px rgba(184, 175, 132, 0.15);
  --border-radius: 12px;
  --transition-speed: 0.2s;
}

/* Floating Button */
.essenca-btn {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 40px;
  height: 100px;
  border-radius: 8px 0 0 8px;
  background-color: var(--primary-color);
  box-shadow: var(--shadow-light);
  cursor: pointer;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-speed) ease, background-color var(--transition-speed) ease;
  border: none;
  overflow: hidden;
}

.essenca-btn:hover {
  transform: translateY(-50%) translateX(-3px);
  background-color: var(--primary-hover);
}

.essenca-btn i,
.essenca-btn .fallback-icon,
.essenca-btn .custom-icon {
  color: white;
  font-size: 20px;
  margin-bottom: 5px;
}

.custom-icon svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.essenca-btn .btn-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  letter-spacing: 1px;
}

/* Popup Container */
.essenca-popup {
  position: fixed;
  top: 50%;
  right: 50px;
  transform: translateY(-50%);
  width: 450px;
  background: #F0EEE5;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  z-index: 9998;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  max-height: 80vh;
  display: none;
  flex-direction: column;
  transition: opacity var(--transition-speed) ease, transform var(--transition-speed) ease;
  opacity: 0;
  transform: translateY(-50%) translateX(10px);
  overflow: hidden;
}

.essenca-popup.active {
  display: flex;
  opacity: 1;
  transform: translateY(-50%) translateX(0);
}

/* Popup Header */
.popup-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
  background-color: var(--primary-color);
  padding: 16px 20px;
  margin: 0;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.popup-header i,
.popup-header .fallback-icon,
.popup-header .custom-icon {
  margin-right: 8px;
  font-size: 18px;
}

.popup-header .custom-icon svg {
  width: 18px;
  height: 18px;
  fill: currentColor;
}

.popup-close {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity var(--transition-speed) ease;
}

.popup-close:hover {
  opacity: 1;
}

/* Chat Container */
.chat-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  scroll-behavior: smooth;
}

/* Message Bubbles */
.message {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.5;
  position: relative;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.ai {
  align-self: flex-start;
  background-color: var(--ai-message-bg);
  border-bottom-left-radius: 4px;
  color: var(--text-color);
}

.message.user {
  align-self: flex-end;
  background-color: var(--user-message-bg);
  border-bottom-right-radius: 4px;
  color: var(--text-color);
}

.message.welcome {
  align-self: center;
  background-color: #F0EEE5;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  text-align: center;
  max-width: 90%;
  color: var(--text-light);
  font-style: italic;
}

/* Message Content Styling */
.message strong {
  color: var(--primary-color);
  font-weight: 600;
}

.message em {
  font-style: italic;
  color: var(--text-light);
}

.message ul {
  margin: 8px 0;
  padding-left: 20px;
}

.message li {
  margin: 4px 0;
  position: relative;
  list-style-type: none;
  padding-left: 20px;
  line-height: 1.5;
}

.message li:before {
  content: "•";
  color: var(--primary-color);
  font-size: 18px;
  position: absolute;
  left: 0;
  top: -2px;
}

/* Code Styling */
.message code {
  background-color: #f4f4f4;
  color: #d73a49;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
  font-size: 13px;
  border: 1px solid #e1e4e8;
}

.message pre {
  background-color: #f8f8f8;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
  position: relative;
}

.message pre code {
  background: none;
  color: var(--text-color);
  padding: 0;
  border-radius: 0;
  border: none;
  font-size: 14px;
  line-height: 1.4;
  display: block;
  white-space: pre;
}

/* Code block scrollbar */
.message pre::-webkit-scrollbar {
  height: 8px;
}

.message pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.message pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.message pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Hot Buttons */
.hot-buttons {
  display: flex;
  gap: 10px;
  padding: 12px 20px;
  background-color: var(--secondary-color);
  border-top: 1px solid var(--border-color);
  flex-shrink: 0;
}

.hot-button {
  flex: 1;
  padding: 10px;
  background-color: #F0EEE5;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  transition: all var(--transition-speed) ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: var(--text-color);
}

.hot-button:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.hot-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.hot-button i,
.hot-button .fallback-icon,
.hot-button .custom-icon {
  font-size: 14px;
}

.hot-button .custom-icon svg {
  width: 14px;
  height: 14px;
  fill: currentColor;
}

/* Input Area */
.input-container {
  display: flex;
  padding: 12px 20px;
  border-top: 1px solid var(--border-color);
  background-color: #F0EEE5;
  flex-shrink: 0;
  align-items: center;
  gap: 10px;
}

.chat-input {
  flex-grow: 1;
  padding: 10px 14px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color var(--transition-speed) ease;
}

.chat-input:focus {
  border-color: var(--primary-color);
}

.send-button {
  width: 60px;
  height: 36px;
  border-radius: 18px;
  background-color: var(--primary-color);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color var(--transition-speed) ease;
  flex-shrink: 0;
}

.send-button:hover {
  background-color: var(--primary-hover);
}

.send-button i,
.send-button .custom-icon {
  font-size: 16px;
}

.send-button .custom-icon svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

/* Loading Message */
.message.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--ai-message-bg);
  padding: 12px 16px;
  color: var(--text-light);
  font-size: 14px;
}

.message.loading i,
.message.loading .fallback-icon {
  color: var(--primary-color);
  font-size: 16px;
}

/* Fallback icon styles */
.fallback-icon {
  display: inline-block;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

/* Spinning animation for fallback loading icon */
.message.loading .fallback-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Scrollbar Styling */
.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: transparent;
}

.chat-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* Icon Styles */
.icon-chat,
.icon-summary,
.icon-idea,
.icon-search,
.icon-settings,
.icon-warning,
.icon-check,
.icon-loading {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  font-weight: bold;
  margin-right: 6px;
  text-transform: uppercase;
}

.icon-chat {
  background-color: var(--primary-color);
}

.icon-summary {
  background-color: #4a6fa5;
}

.icon-idea {
  background-color: #e6a919;
}

.icon-search {
  background-color: #6b7280;
}

.icon-settings {
  background-color: #6b7280;
}

.icon-warning {
  background-color: #dc3545;
}

.icon-check {
  background-color: var(--primary-color);
}

.icon-loading {
  background-color: #6b7280;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

/* Responsive adjustments */
@media (max-width: 500px) {
  .essenca-popup {
    width: 90%;
    right: 50px;
    transform: translateY(-50%);
  }

  .message {
    max-width: 90%;
  }
}