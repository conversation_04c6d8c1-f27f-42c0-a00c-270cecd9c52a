// /scripts/auth.js
// Auth functions for use by popup, options, and content scripts
// Note: background.js has its own inlined auth functions for service worker compatibility

// IMPORTANT: Replace with your actual Supabase details
const SUPABASE_URL = 'https://dnngormeluqzeiwjzyhm.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRubmdvcm1lbHVxemVpd2p6eWhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzY1NjksImV4cCI6MjA2ODg1MjU2OX0.Q2dUR5ojNxq6vfqKNlNKb3byX4ODypgQ44IMDQa-BGs';

// Retrieves the current session from storage
async function getSession() {
  const { essenca_session } = await chrome.storage.local.get('essenca_session');
  return essenca_session;
}

// Refreshes the access token using the refresh token
async function refreshAccessToken() {
  const session = await getSession();
  if (!session?.refresh_token) {
    console.error('❌ No refresh token available');
    throw new Error('No refresh token available.');
  }

  console.log('🔄 Refreshing access token...');

  try {
    const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=refresh_token`, {
      method: 'POST',
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ refresh_token: session.refresh_token })
    });

    if (!response.ok) {
      console.error('❌ Refresh request failed:', response.status, response.statusText);
      await logoutUser(); // Clear invalid session
      throw new Error('Session expired. Please log in again.');
    }

    const data = await response.json();
    console.log('✅ Token refreshed successfully');

    // CRITICAL: Update the ENTIRE session with new data
    const updatedSession = {
      ...session,
      access_token: data.access_token,
      refresh_token: data.refresh_token || session.refresh_token, // Keep old if new not provided
      expires_at: data.expires_at,
      expires_in: data.expires_in
    };

    await chrome.storage.local.set({ 'essenca_session': updatedSession });
    return data.access_token;
  } catch (error) {
    console.error('❌ Token refresh error:', error);
    await logoutUser();
    throw error;
  }
}

// Gets a valid access token, refreshing if necessary
async function getValidAccessToken() {
  const session = await getSession();
  if (!session) throw new Error('User not authenticated.');

  // IMPORTANT: Check if token expires in the next 60 seconds (not just if expired)
  const isExpired = (session.expires_at * 1000) - Date.now() < 60000;
  if (isExpired) {
    console.log('🔄 Token expiring soon, refreshing...');
    return await refreshAccessToken();
  }
  return session.access_token;
}

// Debug function to check token status
async function debugTokenStatus() {
  const session = await getSession();
  if (!session) {
    console.log('❌ No session found');
    return;
  }

  const now = Date.now();
  const expiresAt = session.expires_at * 1000;
  const timeUntilExpiry = expiresAt - now;

  console.log('🔍 Token Debug Info:');
  console.log('- Current time:', new Date(now).toISOString());
  console.log('- Token expires at:', new Date(expiresAt).toISOString());
  console.log('- Time until expiry:', Math.round(timeUntilExpiry / 1000 / 60), 'minutes');
  console.log('- Has refresh token:', !!session.refresh_token);
  console.log('- Access token length:', session.access_token?.length || 0);
}

// Enhanced logout function with cleanup
async function logoutUser() {
  console.log('🔄 Logging out user...');
  await chrome.storage.local.remove(['essenca_session', 'jwtToken']);
  console.log('✅ User logged out successfully');
}
