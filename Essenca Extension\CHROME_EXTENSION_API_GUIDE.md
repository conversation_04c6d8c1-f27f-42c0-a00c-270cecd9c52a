# 🚀 Complete Chrome Extension API Guide for Essenca

This guide provides a full-fledged, tutorial-style blueprint for building a Chrome extension that interacts with the Essenca API. It covers everything from setup and authentication to implementing all major API features.

---

## 📂 **Project Structure**

For a clean and maintainable extension, we recommend structuring your scripts as follows:

```
/scripts
  ├── auth.js      # Handles login, logout, token refresh
  ├── api.js       # Manages all API calls to your backend
  └── background.js  # Background script for the extension
```

---

## ⚙️ **Part 1: Core Authentication (`/scripts/auth.js`)**

This module handles all aspects of user authentication.

### **1.1 Setup & Configuration**

First, define your Supabase credentials. **Never hardcode these in production.** Use your extension's build process to inject them as environment variables.

```javascript
// /scripts/auth.js

// IMPORTANT: Replace with your actual Supabase details
const SUPABASE_URL = 'https://dnngormeluqzeiwjzyhm.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRubmdvcm1lbHVxemVpd2p6eWhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzY1NjksImV4cCI6MjA2ODg1MjU2OX0.Q2dUR5ojNxq6vfqKNlNKb3byX4ODypgQ44IMDQa-BGs';
const API_BASE_URL = 'http://localhost:3000'; // Or your production URL
```

### **1.2 Login, Logout, and Session Management**

```javascript
// /scripts/auth.js (continued)

// Logs the user in and stores the session
async function loginUser(email, password) {
  const response = await fetch(`${API_BASE_URL}/api/essenca/v1/token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  if (!response.ok) throw new Error('Login failed');
  const data = await response.json();
  await chrome.storage.local.set({ 'essenca_session': data });
  return data;
}

// Logs the user out by clearing the session
async function logoutUser() {
  await chrome.storage.local.remove('essenca_session');
}

// Retrieves the current session from storage
async function getSession() {
  const { essenca_session } = await chrome.storage.local.get('essenca_session');
  return essenca_session;
}
```

### **1.3 Automatic Token Refresh**

This is the most critical part. We create a function to get a valid token, refreshing it automatically if it's expired.

```javascript
// /scripts/auth.js (continued)

// Refreshes the access token using the refresh token
async function refreshAccessToken() {
  const session = await getSession();
  if (!session?.refresh_token) throw new Error('No refresh token available.');

  const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=refresh_token`, {
    method: 'POST',
    headers: {
      'apikey': SUPABASE_ANON_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ refresh_token: session.refresh_token })
  });

  if (!response.ok) {
    await logoutUser(); // If refresh fails, log the user out
    throw new Error('Session expired. Please log in again.');
  }

  const data = await response.json();
  // Update the session with the new token data
  await chrome.storage.local.set({ 'essenca_session': { ...session, ...data } });
  return data.access_token;
}

// Gets a valid access token, refreshing if necessary
async function getValidAccessToken() {
  const session = await getSession();
  if (!session) throw new Error('User not authenticated.');

  // Check if token expires in the next 60 seconds
  const isExpired = (session.expires_at * 1000) - Date.now() < 60000;
  if (isExpired) {
    return await refreshAccessToken();
  }
  return session.access_token;
}
```

---

## 📞 **Part 2: API Service Module (`/scripts/api.js`)**

This module will contain functions for every API endpoint. It uses a generic request handler to keep the code DRY (Don't Repeat Yourself).

### **2.1 Generic API Request Handler**

This function handles fetching a valid token, setting headers, and making the request.

```javascript
// /scripts/api.js

// Note: You would need to import getValidAccessToken from './auth.js'
// This is a conceptual guide; for actual extensions, use module bundling.

async function apiRequest(endpoint, method = 'GET', body = null) {
  const accessToken = await getValidAccessToken();
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${accessToken}`
  };

  const config = {
    method,
    headers
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || `API request to ${endpoint} failed`);
  }

  // For GET requests with no content, response.json() can fail
  if (response.status === 204 || response.headers.get('content-length') === '0') {
      return null;
  }
  
  return response.json();
}
```

### **2.2 Implementing All API Endpoints**

Now, create a clean function for each endpoint.

```javascript
// /scripts/api.js (continued)

// --- Public Endpoints ---
export const register = (email, password, username) => 
  apiRequest('/api/essenca/v1/register', 'POST', { email, password, username });

// --- Authenticated User Endpoints ---
export const getMyProfile = () => apiRequest('/api/essenca/v1/user/me');
export const getMyBalance = () => apiRequest('/api/essenca/v1/balance');
export const getMyActivity = () => apiRequest('/api/essenca/v1/user/activity');

export const changePassword = (current_password, new_password) => 
  apiRequest('/api/essenca/v1/user/change-password', 'POST', { current_password, new_password });

export const changeUsername = (password, new_username) => 
  apiRequest('/api/essenca/v1/user/change-username', 'POST', { password, new_username });

// --- Core AI Endpoint ---
export const processContent = (action, content, options = {}) => 
  apiRequest('/api/essenca/v1/process', 'POST', { action, content, ...options });

// --- Admin Endpoints ---
export const getAllUsers = () => apiRequest('/api/admin/users');
export const createAdminUser = (email, password, username) => 
  apiRequest('/api/admin/users/create', 'POST', { email, password, username });
```

---

## 🖥️ **Part 3: Example Usage in Extension UI**

Here’s how you would use these functions in your extension's UI logic (e.g., in a popup script).

```javascript
// /popup/script.js

document.getElementById('login-button').addEventListener('click', async () => {
  const email = document.getElementById('email').value;
  const password = document.getElementById('password').value;
  try {
    await loginUser(email, password);
    console.log('Login successful!');
    // Update UI to show logged-in state
  } catch (error) {
    console.error(error.message);
    // Show error in UI
  }
});

document.getElementById('process-button').addEventListener('click', async () => {
  try {
    const response = await processContent('summarize', 'This is a long text to summarize...');
    console.log('AI Response:', response);
    // Display result in UI
  } catch (error) {
    console.error(error.message);
    // Handle error, maybe prompt for re-login
  }
});
```

This comprehensive guide provides a solid foundation for any developer to successfully build a robust and secure Chrome extension using the Essenca API.

---

## 🔧 **TROUBLESHOOTING: Extension Logout After 1 Hour**

If users are being logged out from the Chrome extension after 1 hour, this indicates that the automatic token refresh mechanism is not working properly. Here's a step-by-step guide to diagnose and fix this issue:

### **🔍 Step 1: Verify Token Refresh Implementation**

**Check if your extension implements the `getValidAccessToken()` function correctly:**

```javascript
// /scripts/auth.js - CRITICAL: This function must be called before EVERY API request

async function getValidAccessToken() {
  const session = await getSession();
  if (!session) throw new Error('User not authenticated.');

  // IMPORTANT: Check if token expires in the next 60 seconds (not just if expired)
  const isExpired = (session.expires_at * 1000) - Date.now() < 60000;
  if (isExpired) {
    console.log('🔄 Token expiring soon, refreshing...');
    return await refreshAccessToken();
  }
  return session.access_token;
}
```

### **🔍 Step 2: Ensure All API Calls Use Token Refresh**

**PROBLEM**: If your extension makes API calls directly without going through `getValidAccessToken()`, tokens will expire.

**SOLUTION**: Update your API request function to ALWAYS use the refresh logic:

```javascript
// /scripts/api.js - UPDATED with better error handling

async function apiRequest(endpoint, method = 'GET', body = null) {
  try {
    // CRITICAL: Always get a valid token (this handles refresh automatically)
    const accessToken = await getValidAccessToken();

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    };

    const config = { method, headers };
    if (body) config.body = JSON.stringify(body);

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

    // Handle 401/403 errors by attempting token refresh
    if (response.status === 401 || response.status === 403) {
      console.log('🔄 Auth error, attempting token refresh...');

      try {
        const newToken = await refreshAccessToken();

        // Retry the request with new token
        const retryResponse = await fetch(`${API_BASE_URL}${endpoint}`, {
          ...config,
          headers: {
            ...headers,
            'Authorization': `Bearer ${newToken}`
          }
        });

        if (!retryResponse.ok) {
          throw new Error('Request failed after token refresh');
        }

        return retryResponse.json();
      } catch (refreshError) {
        console.error('❌ Token refresh failed:', refreshError);
        await logoutUser(); // Force logout if refresh fails
        throw new Error('Session expired. Please log in again.');
      }
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API request to ${endpoint} failed`);
    }

    return response.json();
  } catch (error) {
    console.error('API Request Error:', error);
    throw error;
  }
}
```

### **🔍 Step 3: Fix Token Refresh Function**

**PROBLEM**: The refresh function might not be updating the session correctly.

**SOLUTION**: Ensure proper session update:

```javascript
// /scripts/auth.js - IMPROVED refresh function

async function refreshAccessToken() {
  const session = await getSession();
  if (!session?.refresh_token) {
    console.error('❌ No refresh token available');
    throw new Error('No refresh token available.');
  }

  console.log('🔄 Refreshing access token...');

  try {
    const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=refresh_token`, {
      method: 'POST',
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ refresh_token: session.refresh_token })
    });

    if (!response.ok) {
      console.error('❌ Refresh request failed:', response.status, response.statusText);
      await logoutUser(); // Clear invalid session
      throw new Error('Session expired. Please log in again.');
    }

    const data = await response.json();
    console.log('✅ Token refreshed successfully');

    // CRITICAL: Update the ENTIRE session with new data
    const updatedSession = {
      ...session,
      access_token: data.access_token,
      refresh_token: data.refresh_token || session.refresh_token, // Keep old if new not provided
      expires_at: data.expires_at,
      expires_in: data.expires_in
    };

    await chrome.storage.local.set({ 'essenca_session': updatedSession });
    return data.access_token;
  } catch (error) {
    console.error('❌ Token refresh error:', error);
    await logoutUser();
    throw error;
  }
}
```

### **🔍 Step 4: Add Background Token Refresh**

**PROBLEM**: If the extension is inactive, tokens might expire without refresh.

**SOLUTION**: Add background refresh in your background script:

```javascript
// /scripts/background.js - NEW: Proactive token refresh

// Check and refresh tokens every 30 minutes
chrome.alarms.create('tokenRefresh', { periodInMinutes: 30 });

chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'tokenRefresh') {
    try {
      const session = await chrome.storage.local.get('essenca_session');
      if (session.essenca_session) {
        console.log('🔄 Background token refresh check...');

        // Check if token expires in the next 10 minutes
        const expiresIn = (session.essenca_session.expires_at * 1000) - Date.now();
        if (expiresIn < 600000) { // 10 minutes
          console.log('🔄 Proactively refreshing token in background...');
          await refreshAccessToken();
        }
      }
    } catch (error) {
      console.error('❌ Background refresh failed:', error);
    }
  }
});
```

### **🔍 Step 5: Debug Token Expiration**

**Add debugging to understand what's happening:**

```javascript
// /scripts/auth.js - ADD debugging functions

async function debugTokenStatus() {
  const session = await getSession();
  if (!session) {
    console.log('❌ No session found');
    return;
  }

  const now = Date.now();
  const expiresAt = session.expires_at * 1000;
  const timeUntilExpiry = expiresAt - now;

  console.log('🔍 Token Debug Info:');
  console.log('- Current time:', new Date(now).toISOString());
  console.log('- Token expires at:', new Date(expiresAt).toISOString());
  console.log('- Time until expiry:', Math.round(timeUntilExpiry / 1000 / 60), 'minutes');
  console.log('- Has refresh token:', !!session.refresh_token);
  console.log('- Access token length:', session.access_token?.length || 0);
}

// Call this function in your popup or content script to debug
// debugTokenStatus();
```

### **🔍 Step 6: Update Manifest Permissions**

**Ensure your manifest.json has the required permissions:**

```json
{
  "manifest_version": 3,
  "permissions": [
    "storage",
    "alarms",
    "activeTab"
  ],
  "host_permissions": [
    "https://your-api-domain.com/*",
    "https://your-supabase-url.supabase.co/*"
  ]
}
```

### **🔍 Step 7: Common Issues and Fixes**

**Issue 1: Token refresh not being called**
- **Check**: Are you calling `getValidAccessToken()` before EVERY API request?
- **Fix**: Replace direct `session.access_token` usage with `await getValidAccessToken()`

**Issue 2: Refresh token expired**
- **Check**: Look for "No refresh token available" errors
- **Fix**: Implement proper error handling to redirect to login

**Issue 3: Background script not running**
- **Check**: Verify background script is registered in manifest
- **Fix**: Add background script and alarm permissions

**Issue 4: CORS issues during refresh**
- **Check**: Network tab for CORS errors during refresh
- **Fix**: Ensure Supabase URL is in host_permissions

### **🔍 Step 8: Testing the Fix**

**Test your implementation:**

1. **Login to extension**
2. **Wait 50 minutes** (or change expires_at to test faster)
3. **Make an API call** - should auto-refresh
4. **Check console logs** for refresh messages
5. **Verify session is updated** in Chrome storage

### **🔍 Step 9: Emergency Fallback**

**If automatic refresh fails, implement graceful degradation:**

```javascript
// Add to your API functions
async function makeAuthenticatedRequest(endpoint, options = {}) {
  try {
    return await apiRequest(endpoint, options.method, options.body);
  } catch (error) {
    if (error.message.includes('Session expired') || error.message.includes('Invalid token')) {
      // Show login prompt to user
      console.log('🔑 Session expired, prompting for re-login...');
      // Redirect to login or show login popup
      return null;
    }
    throw error;
  }
}
```

### **🚨 Quick Fix Checklist:**

- [ ] `getValidAccessToken()` is called before every API request
- [ ] Token refresh happens 60 seconds before expiry
- [ ] Background script implements periodic refresh checks
- [ ] Error handling redirects to login when refresh fails
- [ ] Manifest includes required permissions
- [ ] Console logs show refresh activity
- [ ] Session storage is properly updated after refresh

**Most likely cause**: Your extension is not calling `getValidAccessToken()` before API requests, or the refresh mechanism is not properly implemented.
