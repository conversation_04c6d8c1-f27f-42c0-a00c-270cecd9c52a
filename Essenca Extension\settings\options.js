// Default system prompt
const DEFAULT_SYSTEM_PROMPT = `Read the following page content and create a concise bullet-point summary that captures:
• Main topic and key arguments
• Important facts and statistics
• Key takeaways and conclusions
Format the response as clear, easy-to-read bullet points.`;

// DOM Elements
const apiKeyInput = document.getElementById('apiKey');
const modelInput = document.getElementById('model');
const systemPromptTextarea = document.getElementById('systemPrompt');
const userProfileInfoTextarea = document.getElementById('userProfileInfo');
const userProfileFileInput = document.getElementById('userProfileFile');
const saveButton = document.getElementById('save');
const resetButton = document.getElementById('reset');
const statusDiv = document.getElementById('status');
const modelOptions = document.querySelectorAll('.model-option');
const providerSelect = document.getElementById('provider');
const openaiApiKeyInput = document.getElementById('openaiApiKey');
const geminiApiKeyInput = document.getElementById('geminiApiKey');
const modelOptionsOpenAI = document.getElementById('modelOptionsOpenAI');
const modelOptionsGemini = document.getElementById('modelOptionsGemini');
const modelHintOpenAI = document.getElementById('modelHintOpenAI');
const modelHintGemini = document.getElementById('modelHintGemini');
const openaiApiGroup = document.getElementById('openai-api-group');
const geminiApiGroup = document.getElementById('gemini-api-group');
const essencaApiGroup = document.getElementById('essenca-api-group');

// Auth Modal Elements
const authModal = document.getElementById('auth-modal');
const loginBtn = document.getElementById('login-btn');
const closeModalBtn = document.querySelector('.close-button');
const showRegisterLink = document.getElementById('show-register');
const showLoginLink = document.getElementById('show-login');
const loginView = document.getElementById('login-view');
const registerView = document.getElementById('register-view');
const submitLoginBtn = document.getElementById('submit-login');
const submitRegisterBtn = document.getElementById('submit-register');
const logoutBtn = document.getElementById('logout-btn');
const authStatusDiv = document.getElementById('auth-status');

// API URL
const API_BASE_URL = 'https://essenca-admin.vercel.app/api/essenca/v1';

// Load saved settings
function loadSettings() {
    chrome.storage.local.get(['jwtToken', 'userEmail'], (localData) => {
        if (localData.jwtToken) {
            updateAuthUI(true, localData.userEmail);
            fetchTokenBalance(localData.jwtToken);
        } else {
            updateAuthUI(false);
        }
    });

    chrome.storage.sync.get(
        {
            provider: 'essenca_api',
            openaiApiKey: '',
            geminiApiKey: '',
            model: 'gemini-2.5-flash',
            systemPrompt: '',
            userProfileInfo: ''
        },
        (items) => {
            providerSelect.value = items.provider;
            openaiApiKeyInput.value = items.openaiApiKey;
            geminiApiKeyInput.value = items.geminiApiKey;
            modelInput.value = items.model;
            systemPromptTextarea.value = items.systemPrompt;
            userProfileInfoTextarea.value = items.userProfileInfo;
            updateProviderUI(items.provider);
            updateModelOptionSelection(items.model);
        }
    );
}

// Update model option selection
function updateModelOptionSelection(selectedModel) {
    let options = providerSelect.value === 'openai' ? modelOptionsOpenAI : modelOptionsGemini;
    Array.from(options.querySelectorAll('.model-option')).forEach(option => {
        if (option.dataset.model === selectedModel) {
            option.classList.add('selected');
        } else {
            option.classList.remove('selected');
        }
    });
}

// Save settings
function saveSettings() {
    const provider = providerSelect.value;
    const openaiApiKey = openaiApiKeyInput.value.trim();
    const geminiApiKey = geminiApiKeyInput.value.trim();
    const model = modelInput.value.trim();
    const systemPrompt = systemPromptTextarea.value.trim();
    const userProfileInfo = userProfileInfoTextarea.value.trim();

    if (provider === 'openai' && !openaiApiKey) {
        showStatus('Error: OpenAI API key is required.', 'error');
        return;
    }
    if (provider === 'gemini' && !geminiApiKey) {
        showStatus('Error: Gemini API key is required.', 'error');
        return;
    }
    if (provider !== 'essenca_api' && !model) {
        showStatus('Error: Model name cannot be empty', 'error');
        return;
    }

    chrome.storage.sync.set(
        {
            provider: provider,
            openaiApiKey: openaiApiKey,
            geminiApiKey: geminiApiKey,
            model: model,
            systemPrompt: systemPrompt,
            userProfileInfo: userProfileInfo
        },
        () => {
            if (chrome.runtime.lastError) {
                showStatus('Error saving settings: ' + chrome.runtime.lastError.message, 'error');
            } else {
                showStatus('Settings saved successfully!', 'success');
            }
        }
    );
}

// Reset settings to defaults
function resetSettings() {
    providerSelect.value = 'openai';
    openaiApiKeyInput.value = '';
    geminiApiKeyInput.value = '';
    modelInput.value = 'gpt-3.5-turbo';
    systemPromptTextarea.value = '';
    userProfileInfoTextarea.value = '';
    updateProviderUI('openai');
    updateModelOptionSelection('gpt-3.5-turbo');
    showStatus('Settings reset to defaults. Click Save to apply changes.', 'success');
}

// Show status message
function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = type;
    statusDiv.style.display = 'block';

    // Hide status after 5 seconds
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 5000);
}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    loadSettings();

    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', () => {
            const tab = link.dataset.tab;

            tabLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');

            tabContents.forEach(content => {
                if (content.id === tab) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });
        });
    });

    const toggleApiKeys = document.querySelectorAll('.toggle-api-key');
    toggleApiKeys.forEach(toggle => {
        toggle.addEventListener('click', () => {
            const input = toggle.previousElementSibling;
            if (input.type === 'password') {
                input.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        });
    });
});
saveButton.addEventListener('click', saveSettings);
resetButton.addEventListener('click', resetSettings);
userProfileFileInput.addEventListener('change', handleFileSelect);

// Handle model option selection
Array.from(document.querySelectorAll('.model-option')).forEach(option => {
    option.addEventListener('click', () => {
        const selectedModel = option.dataset.model;
        modelInput.value = selectedModel;
        updateModelOptionSelection(selectedModel);
    });
});

function updateProviderUI(provider) {
    openaiApiGroup.style.display = 'none';
    geminiApiGroup.style.display = 'none';
    essencaApiGroup.style.display = 'none';
    modelInput.parentElement.style.display = 'block';
    modelOptionsOpenAI.style.display = 'none';
    modelOptionsGemini.style.display = 'none';
    modelHintOpenAI.style.display = 'none';
    modelHintGemini.style.display = 'none';


    if (provider === 'openai') {
        openaiApiGroup.style.display = 'block';
        modelOptionsOpenAI.style.display = 'flex';
        modelHintOpenAI.style.display = 'block';
        modelInput.placeholder = 'gpt-3.5-turbo';
    } else if (provider === 'gemini') {
        geminiApiGroup.style.display = 'block';
        modelOptionsGemini.style.display = 'flex';
        modelHintGemini.style.display = 'block';
        modelInput.placeholder = 'gemini-pro';
    } else if (provider === 'essenca_api') {
        essencaApiGroup.style.display = 'block';
        modelInput.parentElement.style.display = 'none'; // Hide model selection
    }
}

providerSelect.addEventListener('change', function () {
    const newProvider = this.value;
    updateProviderUI(newProvider);

    // When provider changes, auto-save and set a default model for better UX
    let defaultModel = '';
    if (newProvider === 'openai') {
        defaultModel = 'gpt-3.5-turbo';
    } else if (newProvider === 'gemini') {
        defaultModel = 'gemini-2.5-flash'; // A sensible default
    }

    const settingsToSave = { provider: newProvider };

    if (newProvider !== 'essenca_api') {
        modelInput.value = defaultModel;
        updateModelOptionSelection(defaultModel);
        settingsToSave.model = defaultModel;
    } else {
        // For Essenca API, model is not user-configurable, so we can clear it.
        modelInput.value = '';
        updateModelOptionSelection('');
        settingsToSave.model = ''; // Save empty model
    }

    chrome.storage.sync.set(settingsToSave, () => {
        if (chrome.runtime.lastError) {
            console.error(`Error auto-saving provider/model: ${chrome.runtime.lastError.message}`);
        }
    });
});

// --- Auth Modal Logic ---
loginBtn.addEventListener('click', () => {
    authModal.style.display = 'flex';
    clearAuthStatus();
    // Focus on the first input
    setTimeout(() => {
        document.getElementById('login-username').focus();
    }, 100);
});

closeModalBtn.addEventListener('click', () => {
    authModal.style.display = 'none';
    clearAuthStatus();
});

showRegisterLink.addEventListener('click', (e) => {
    e.preventDefault();
    loginView.style.display = 'none';
    registerView.style.display = 'block';
    clearAuthStatus();
    // Focus on the first input
    setTimeout(() => {
        document.getElementById('register-username').focus();
    }, 100);
});

showLoginLink.addEventListener('click', (e) => {
    e.preventDefault();
    registerView.style.display = 'none';
    loginView.style.display = 'block';
    clearAuthStatus();
    // Focus on the first input
    setTimeout(() => {
        document.getElementById('login-username').focus();
    }, 100);
});

window.addEventListener('click', (e) => {
    if (e.target === authModal) {
        authModal.style.display = 'none';
        clearAuthStatus();
    }
});

// Add Enter key support for login form
document.getElementById('login-password').addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !submitLoginBtn.disabled) {
        submitLoginBtn.click();
    }
});

// Add Enter key support for register form
document.getElementById('register-password').addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !submitRegisterBtn.disabled) {
        submitRegisterBtn.click();
    }
});

// Password toggle functionality
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('password-toggle')) {
        const targetId = e.target.getAttribute('data-target');
        const passwordInput = document.getElementById(targetId);
        const toggleIcon = e.target;
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
});

// --- Authentication Functions ---
submitRegisterBtn.addEventListener('click', async () => {
    const username = document.getElementById('register-username').value.trim();
    const email = document.getElementById('register-email').value.trim();
    const password = document.getElementById('register-password').value;

    // Basic validation
    if (!username || !email || !password) {
        showAuthStatus('Please fill in all fields.', 'error');
        return;
    }

    if (password.length < 6) {
        showAuthStatus('Password must be at least 6 characters long.', 'error');
        return;
    }

    // Set loading state
    setButtonLoading(submitRegisterBtn, true);
    clearAuthStatus();

    try {
        const response = await fetch(`${API_BASE_URL}/register`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password, username })
        });
        const data = await response.json();
        if (!response.ok) throw new Error(data.error || 'Registration failed.');

        showAuthStatus('Registration successful! Please log in.', 'success');
        // Clear form
        document.getElementById('register-username').value = '';
        document.getElementById('register-email').value = '';
        document.getElementById('register-password').value = '';
        // Switch to login view after a short delay
        setTimeout(() => {
            showLoginLink.click();
        }, 1500);
    } catch (error) {
        showAuthStatus(error.message, 'error');
    } finally {
        setButtonLoading(submitRegisterBtn, false);
    }
});

submitLoginBtn.addEventListener('click', async () => {
    const email = document.getElementById('login-username').value.trim(); // Can accept email or username
    const password = document.getElementById('login-password').value;

    // Basic validation
    if (!email || !password) {
        showAuthStatus('Please fill in all fields.', 'error');
        return;
    }

    // Set loading state
    setButtonLoading(submitLoginBtn, true);
    clearAuthStatus();

    try {
        const response = await fetch(`${API_BASE_URL}/token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password })
        });
        const data = await response.json();
        if (!response.ok) throw new Error(data.error || 'Login failed.');

        // Store the full session data from Next.js backend
        chrome.storage.local.set({
            essenca_session: data,
            jwtToken: data.access_token, // Keep for backward compatibility
            userEmail: data.user?.email || email
        }, () => {
            updateAuthUI(true, data.user?.email || email);
            fetchTokenBalance(data.access_token);
            authModal.style.display = 'none';
            showStatus('Logged in successfully!', 'success');
            // Clear form
            document.getElementById('login-username').value = '';
            document.getElementById('login-password').value = '';
        });
    } catch (error) {
        showAuthStatus(error.message, 'error');
    } finally {
        setButtonLoading(submitLoginBtn, false);
    }
});

logoutBtn.addEventListener('click', () => {
    // Add loading state to logout button
    setButtonLoading(logoutBtn, true);
    
    // Perform logout directly
    chrome.storage.local.remove(['jwtToken', 'userEmail', 'essenca_session'], () => {
        updateAuthUI(false);
        showStatus('Logged out successfully.', 'success');
        setButtonLoading(logoutBtn, false);
    });
});

function updateAuthUI(isLoggedIn, email = '') {
    const loggedInView = document.getElementById('auth-logged-in');
    const loggedOutView = document.getElementById('auth-logged-out');
    if (isLoggedIn) {
        loggedInView.style.display = 'block';
        loggedOutView.style.display = 'none';
        document.getElementById('user-email').textContent = email;
    } else {
        loggedInView.style.display = 'none';
        loggedOutView.style.display = 'block';
        document.getElementById('user-email').textContent = '';
        document.getElementById('token-balance').textContent = 'N/A';
    }
}

async function fetchTokenBalance(token) {
    try {
        const response = await fetch(`${API_BASE_URL}/balance`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });
        const data = await response.json();
        if (!response.ok) throw new Error(data.error || 'Could not fetch balance.');

        document.getElementById('token-balance').textContent = data.balance;
    } catch (error) {
        console.error('Error fetching token balance:', error);
        document.getElementById('token-balance').textContent = 'Error';
    }
}

function showAuthStatus(message, type) {
    authStatusDiv.textContent = message;
    authStatusDiv.className = type;
    authStatusDiv.style.display = 'block';
    
    // Auto-hide success messages after 3 seconds
    if (type === 'success') {
        setTimeout(() => {
            clearAuthStatus();
        }, 3000);
    }
}

function clearAuthStatus() {
    authStatusDiv.style.display = 'none';
    authStatusDiv.className = '';
    authStatusDiv.textContent = '';
}

function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.classList.add('loading');
        button.disabled = true;
    } else {
        button.classList.remove('loading');
        button.disabled = false;
    }
}


// Handle file upload for personalization
function handleFileSelect(event) {
    const file = event.target.files[0];
    const fileNameSpan = document.getElementById('fileName');
    
    if (!file) {
        fileNameSpan.textContent = 'No file chosen';
        fileNameSpan.classList.remove('has-file');
        return;
    }
    
    // Allow both .txt and .md files. Note: MIME type for .md can be inconsistent.
    // A simple check for file extension is more reliable here.
    if (!file.name.endsWith('.txt') && !file.name.endsWith('.md')) {
        showStatus('Error: Please upload a .txt or .md file.', 'error');
        fileNameSpan.textContent = 'No file chosen';
        fileNameSpan.classList.remove('has-file');
        return;
    }

    // Update file name display
    fileNameSpan.textContent = file.name;
    fileNameSpan.classList.add('has-file');

    const reader = new FileReader();
    reader.onload = function (e) {
        userProfileInfoTextarea.value = e.target.result;
        showStatus(`Successfully loaded ${file.name}. Click Save to apply changes.`, 'success');
    };
    reader.onerror = function () {
        showStatus(`Error reading file: ${reader.error}`, 'error');
        fileNameSpan.textContent = 'No file chosen';
        fileNameSpan.classList.remove('has-file');
    };
    reader.readAsText(file);
}
