<!DOCTYPE html>
<html>

<head>
    <title>Essenca</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            box-sizing: border-box;
        }

        :root {
            --primary-color: #DA7756;
            --primary-hover: #BD5D3A;
            --text-color: #3D3929;
            --text-light: #6D6A58;
            --border-color: #CBC4A4;
        }

        body {
            width: 280px;
            padding: 20px 30px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: var(--text-color);
            margin: 0;
            background-color: #F0EEE5;
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo {
            font-size: 24px;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .logo svg {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        h2 {
            color: var(--text-color);
            font-size: 17px;
            margin: 0;
            flex-grow: 1;
        }

        .settings-btn {
            color: var(--text-light);
            font-size: 18px;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .settings-btn:hover {
            color: var(--primary-color);
        }

        .button {
            display: flex;
            width: 100%;
            padding: 10px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            font-size: 14px;
            margin-bottom: 15px;
            justify-content: center;
            align-items: center;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }

        .button:hover {
            background-color: var(--primary-hover);
        }

        .button-icon {
            margin-right: 8px;
        }

        .button-icon svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        .status-container {
            background-color: #DDD9C5;
            border-radius: 6px;
            padding: 12px;
        }

        .status {
            font-size: 13px;
            color: var(--text-light);
            display: grid;
            grid-template-columns: auto 1fr;
            align-items: center;
            grid-gap: 5px 10px;
        }

        .status-icon {
            grid-row: 1 / span 2;
            margin-right: 8px;
            font-size: 16px;
            align-self: center;
        }

        .status-text-main {
            font-weight: 500;
            color: var(--text-color);
        }

        .status-text-secondary {
            grid-column: 2;
        }

        .not-configured {
            color: #dc3545;
        }

        .configured {
            color: var(--primary-color);
        }

        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 15px 0;
        }

        .footer {
            font-size: 11px;
            color: var(--text-light);
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="logo">
            <svg version="1.0" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 900.000000 900.000000" preserveAspectRatio="xMidYMid meet">
                <g transform="translate(0.000000,900.000000) scale(0.100000,-0.100000)" fill="currentColor"
                    stroke="none">
                    <path
                        d="M4428 8433 c-6 -7 -21 -13 -35 -13 -31 0 -66 -23 -125 -82 -32 -31 -48 -56 -48 -72 0 -36 18 -41 32 -9 7 15 23 38 36 52 12 13 22 28 22 32 0 5 6 9 14 9 7 0 19 7 26 15 7 8 23 15 36 15 12 0 26 5 29 10 9 14 117 12 166 -2 46 -14 126 -86 147 -131 14 -32 32 -27 32 9 0 16 -21 44 -66 89 -54 53 -72 65 -98 65 -18 0 -37 5 -44 12 -16 16 -111 16 -124 1z" />
                    <path
                        d="M4190 7061 c0 -1120 2 -1193 28 -1167 3 4 6 528 7 1164 0 1094 -1 1157 -17 1160 -17 3 -18 -57 -18 -1157z" />
                    <path
                        d="M4760 8218 c0 -1 -1 -525 -1 -1163 -1 -1060 0 -1160 15 -1163 15 -3 16 94 14 1159 -2 919 -6 1163 -15 1167 -7 2 -13 2 -13 0z" />
                </g>
            </svg>
        </div>
        <h2>Essenca</h2>
        <a href="#" id="openOptions" class="settings-btn" title="Settings">
            <i class="fas fa-cog"></i>
        </a>
    </div>

    <a href="#" id="activateAssistant" class="button">
        <span class="button-icon">
            <svg version="1.0" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                viewBox="0 0 900.000000 900.000000" preserveAspectRatio="xMidYMid meet">
                <g transform="translate(0.000000,900.000000) scale(0.100000,-0.100000)" fill="currentColor"
                    stroke="none">
                    <path
                        d="M4428 8433 c-6 -7 -21 -13 -35 -13 -31 0 -66 -23 -125 -82 -32 -31 -48 -56 -48 -72 0 -36 18 -41 32 -9 7 15 23 38 36 52 12 13 22 28 22 32 0 5 6 9 14 9 7 0 19 7 26 15 7 8 23 15 36 15 12 0 26 5 29 10 9 14 117 12 166 -2 46 -14 126 -86 147 -131 14 -32 32 -27 32 9 0 16 -21 44 -66 89 -54 53 -72 65 -98 65 -18 0 -37 5 -44 12 -16 16 -111 16 -124 1z" />
                    <path
                        d="M4190 7061 c0 -1120 2 -1193 28 -1167 3 4 6 528 7 1164 0 1094 -1 1157 -17 1160 -17 3 -18 -57 -18 -1157z" />
                    <path
                        d="M4760 8218 c0 -1 -1 -525 -1 -1163 -1 -1060 0 -1160 15 -1163 15 -3 16 94 14 1159 -2 919 -6 1163 -15 1167 -7 2 -13 2 -13 0z" />
                </g>
            </svg>
        </span>
        Analyze Current Page
    </a>

    <div class="status-container">
        <div id="status" class="status">
            <span class="status-icon"><i class="fas fa-spinner fa-spin"></i></span>
            Loading settings...
        </div>
    </div>

    <div class="divider"></div>

    <div class="footer">
        Essenca &bull; v1.1.0
    </div>

    <script src="popup.js"></script>
</body>

</html>