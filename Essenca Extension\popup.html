<!DOCTYPE html>
<html>

<head>
    <title>Essenca</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            box-sizing: border-box;
        }

        :root {
            --primary-color: #DA7756;
            --primary-hover: #BD5D3A;
            --text-color: #3D3929;
            --text-light: #6D6A58;
            --border-color: #CBC4A4;
        }

        body {
            width: 280px;
            padding: 20px 30px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: var(--text-color);
            margin: 0;
            background-color: #F0EEE5;
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo {
            font-size: 24px;
            margin-right: 10px;
            color: var(--primary-color);
        }

        h2 {
            color: var(--text-color);
            font-size: 17px;
            margin: 0;
            flex-grow: 1;
        }

        .settings-btn {
            color: var(--text-light);
            font-size: 18px;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .settings-btn:hover {
            color: var(--primary-color);
        }

        .button {
            display: flex;
            width: 100%;
            padding: 10px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            font-size: 14px;
            margin-bottom: 15px;
            justify-content: center;
            align-items: center;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }

        .button:hover {
            background-color: var(--primary-hover);
        }

        .button-icon {
            margin-right: 8px;
        }

        .status-container {
            background-color: #DDD9C5;
            border-radius: 6px;
            padding: 12px;
        }

        .status {
            font-size: 13px;
            color: var(--text-light);
            display: grid;
            grid-template-columns: auto 1fr;
            align-items: center;
            grid-gap: 5px 10px;
        }

        .status-icon {
            grid-row: 1 / span 2;
            margin-right: 8px;
            font-size: 16px;
            align-self: center;
        }

        .status-text-main {
            font-weight: 500;
            color: var(--text-color);
        }

        .status-text-secondary {
            grid-column: 2;
        }

        .not-configured {
            color: #dc3545;
        }

        .configured {
            color: var(--primary-color);
        }

        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 15px 0;
        }

        .footer {
            font-size: 11px;
            color: var(--text-light);
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="logo"><i class="fas fa-leaf"></i></div>
        <h2>Essenca</h2>
        <a href="#" id="openOptions" class="settings-btn" title="Settings">
            <i class="fas fa-cog"></i>
        </a>
    </div>

    <a href="#" id="activateAssistant" class="button">
        <span class="button-icon"><i class="fas fa-feather-alt"></i></span>
        Analyze Current Page
    </a>

    <div class="status-container">
        <div id="status" class="status">
            <span class="status-icon"><i class="fas fa-spinner fa-spin"></i></span>
            Loading settings...
        </div>
    </div>

    <div class="divider"></div>

    <div class="footer">
        Essenca &bull; v1.1.0
    </div>

    <script src="popup.js"></script>
</body>

</html>